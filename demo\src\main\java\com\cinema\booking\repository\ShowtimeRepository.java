package com.cinema.booking.repository;

import com.cinema.booking.model.Showtime;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

@Repository
public interface ShowtimeRepository extends JpaRepository<Showtime, Long> {
    
    /**
     * Find showtimes by movie ID
     */
    List<Showtime> findByMovieId(Long movieId);
    
    /**
     * Find showtimes by theater ID
     */
    List<Showtime> findByTheaterId(Long theaterId);
    
    /**
     * Find showtimes by date
     */
    List<Showtime> findByShowDate(LocalDate showDate);
    
    /**
     * Find showtimes by date range
     */
    List<Showtime> findByShowDateBetween(LocalDate startDate, LocalDate endDate);
    
    /**
     * Find showtimes for a specific movie and date
     */
    List<Showtime> findByMovieIdAndShowDate(Long movieId, LocalDate showDate);
    
    /**
     * Find showtimes for a specific theater and date
     */
    List<Showtime> findByTheaterIdAndShowDate(Long theaterId, LocalDate showDate);
    
    /**
     * Find showtimes with available seats
     */
    @Query("SELECT s FROM Showtime s WHERE s.availableSeats > 0")
    List<Showtime> findShowtimesWithAvailableSeats();
    
    /**
     * Find upcoming showtimes (from current date/time)
     */
    @Query("SELECT s FROM Showtime s WHERE s.showDate > :currentDate OR " +
           "(s.showDate = :currentDate AND s.showTime > :currentTime)")
    List<Showtime> findUpcomingShowtimes(@Param("currentDate") LocalDate currentDate, 
                                        @Param("currentTime") LocalTime currentTime);
    
    /**
     * Find showtimes for today
     */
    @Query("SELECT s FROM Showtime s WHERE s.showDate = :today AND s.showTime > :currentTime")
    List<Showtime> findTodaysUpcomingShowtimes(@Param("today") LocalDate today, 
                                              @Param("currentTime") LocalTime currentTime);
    
    /**
     * Find showtimes by movie with pagination
     */
    Page<Showtime> findByMovieId(Long movieId, Pageable pageable);
    
    /**
     * Find showtimes by theater with pagination
     */
    Page<Showtime> findByTheaterId(Long theaterId, Pageable pageable);
    
    /**
     * Find showtimes for a movie in a specific theater
     */
    List<Showtime> findByMovieIdAndTheaterId(Long movieId, Long theaterId);
    
    /**
     * Find showtimes by time range on a specific date
     */
    @Query("SELECT s FROM Showtime s WHERE s.showDate = :showDate AND s.showTime BETWEEN :startTime AND :endTime")
    List<Showtime> findByDateAndTimeRange(@Param("showDate") LocalDate showDate,
                                         @Param("startTime") LocalTime startTime,
                                         @Param("endTime") LocalTime endTime);
    
    /**
     * Find popular showtimes (most bookings)
     */
    @Query("SELECT s FROM Showtime s LEFT JOIN s.bookings b " +
           "WHERE s.showDate >= :fromDate " +
           "GROUP BY s.id ORDER BY COUNT(b) DESC")
    List<Showtime> findPopularShowtimes(@Param("fromDate") LocalDate fromDate, Pageable pageable);
    
    /**
     * Find showtimes with minimum available seats
     */
    @Query("SELECT s FROM Showtime s WHERE s.availableSeats >= :minSeats AND s.showDate >= :fromDate")
    List<Showtime> findShowtimesWithMinimumSeats(@Param("minSeats") Integer minSeats, 
                                                 @Param("fromDate") LocalDate fromDate);
    
    /**
     * Count showtimes by movie
     */
    @Query("SELECT COUNT(s) FROM Showtime s WHERE s.movie.id = :movieId")
    long countByMovieId(@Param("movieId") Long movieId);
    
    /**
     * Count showtimes by theater
     */
    @Query("SELECT COUNT(s) FROM Showtime s WHERE s.theater.id = :theaterId")
    long countByTheaterId(@Param("theaterId") Long theaterId);
    
    /**
     * Find showtimes by price range
     */
    @Query("SELECT s FROM Showtime s WHERE s.basePrice BETWEEN :minPrice AND :maxPrice")
    List<Showtime> findByPriceRange(@Param("minPrice") java.math.BigDecimal minPrice,
                                   @Param("maxPrice") java.math.BigDecimal maxPrice);
    
    /**
     * Find conflicting showtimes for a theater (overlapping times)
     */
    @Query("SELECT s FROM Showtime s WHERE s.theater.id = :theaterId AND s.showDate = :showDate AND " +
           "((s.showTime <= :endTime AND s.showTime >= :startTime) OR " +
           "(s.showTime <= :startTime AND DATE_ADD(s.showTime, INTERVAL :durationMinutes MINUTE) > :startTime))")
    List<Showtime> findConflictingShowtimes(@Param("theaterId") Long theaterId,
                                           @Param("showDate") LocalDate showDate,
                                           @Param("startTime") LocalTime startTime,
                                           @Param("endTime") LocalTime endTime,
                                           @Param("durationMinutes") Integer durationMinutes);
}
