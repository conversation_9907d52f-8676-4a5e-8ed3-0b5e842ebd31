package com.cinema.booking.service;

import com.cinema.booking.exception.ResourceNotFoundException;
import com.cinema.booking.model.Theater;
import com.cinema.booking.repository.TheaterRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;

@Service
@Transactional
public class TheaterService {

    private static final Logger logger = LoggerFactory.getLogger(TheaterService.class);

    @Autowired
    private TheaterRepository theaterRepository;

    public Theater createTheater(Theater theater) {
        logger.info("Creating new theater: {}", theater.getName());

        Theater savedTheater = theaterRepository.save(theater);
        logger.info("Theater created successfully with ID: {}", savedTheater.getId());
        
        return savedTheater;
    }

    public Theater getTheaterById(Long id) {
        return theaterRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Theater", "id", id));
    }

    public Theater updateTheater(Long id, Theater theaterDetails) {
        logger.info("Updating theater with ID: {}", id);

        Theater theater = getTheaterById(id);

        theater.setName(theaterDetails.getName());
        theater.setLocation(theaterDetails.getLocation());
        theater.setTotalSeats(theaterDetails.getTotalSeats());
        theater.setScreenType(theaterDetails.getScreenType());

        Theater updatedTheater = theaterRepository.save(theater);
        logger.info("Theater updated successfully: {}", updatedTheater.getId());
        
        return updatedTheater;
    }

    public void deleteTheater(Long id) {
        logger.info("Deleting theater with ID: {}", id);

        Theater theater = getTheaterById(id);
        theaterRepository.delete(theater);
        
        logger.info("Theater deleted successfully: {}", id);
    }

    public List<Theater> getAllTheaters() {
        return theaterRepository.findAll();
    }

    public List<Theater> getTheatersByScreenType(Theater.ScreenType screenType) {
        return theaterRepository.findByScreenType(screenType);
    }

    public List<Theater> getTheatersByLocation(String location) {
        return theaterRepository.findByLocationContaining(location);
    }

    public List<Theater> searchTheatersByName(String name) {
        return theaterRepository.findByNameContaining(name);
    }

    public List<Theater> getTheatersByMinimumSeats(Integer minSeats) {
        return theaterRepository.findByMinimumSeats(minSeats);
    }

    public List<Theater> getTheatersBySeatCapacityRange(Integer minSeats, Integer maxSeats) {
        return theaterRepository.findBySeatCapacityRange(minSeats, maxSeats);
    }

    public List<Theater> getTheatersWithShowtimesOnDate(LocalDate date) {
        return theaterRepository.findTheatersWithShowtimesOnDate(date);
    }

    public List<Theater> getTheatersShowingMovie(Long movieId) {
        return theaterRepository.findTheatersShowingMovie(movieId, LocalDate.now());
    }

    public List<Theater> getTheatersByCapacityDesc() {
        return theaterRepository.findAllByOrderByTotalSeatsDesc();
    }

    public List<Theater> get3DTheaters() {
        return theaterRepository.findTheatersSupporting3D();
    }

    public List<Theater> getIMAXTheaters() {
        return theaterRepository.findByScreenType_IMAX();
    }

    public long getTheaterCount() {
        return theaterRepository.count();
    }

    public long getTheaterCountByScreenType(Theater.ScreenType screenType) {
        return theaterRepository.countByScreenType(screenType);
    }

    public List<Theater> getTheatersWithAvailableSeats(Long showtimeId) {
        return theaterRepository.findTheatersWithAvailableSeats(showtimeId);
    }

    // Utility methods
    public boolean theaterExists(Long id) {
        return theaterRepository.existsById(id);
    }

    public Theater getTheaterWithSeats(Long id) {
        Theater theater = getTheaterById(id);
        // Force loading of seats if needed
        theater.getSeats().size();
        return theater;
    }

    public Theater getTheaterWithShowtimes(Long id) {
        Theater theater = getTheaterById(id);
        // Force loading of showtimes if needed
        theater.getShowtimes().size();
        return theater;
    }
}
