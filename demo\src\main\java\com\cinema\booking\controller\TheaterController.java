package com.cinema.booking.controller;

import com.cinema.booking.dto.response.ApiResponse;
import com.cinema.booking.model.Theater;
import com.cinema.booking.service.TheaterService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/theaters")
@CrossOrigin(origins = "*", maxAge = 3600)
public class TheaterController {

    private static final Logger logger = LoggerFactory.getLogger(TheaterController.class);

    @Autowired
    private TheaterService theaterService;

    @GetMapping
    public ResponseEntity<ApiResponse<List<Theater>>> getAllTheaters() {
        logger.info("Fetching all theaters");

        try {
            List<Theater> theaters = theaterService.getAllTheaters();
            ApiResponse<List<Theater>> response = ApiResponse.success("Theaters retrieved successfully", theaters);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch theaters", e);
            ApiResponse<List<Theater>> response = ApiResponse.error("Failed to fetch theaters: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<Theater>> getTheaterById(@PathVariable Long id) {
        logger.info("Fetching theater with ID: {}", id);

        try {
            Theater theater = theaterService.getTheaterById(id);
            ApiResponse<Theater> response = ApiResponse.success("Theater retrieved successfully", theater);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch theater with ID: {}", id, e);
            ApiResponse<Theater> response = ApiResponse.error("Failed to fetch theater: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/{id}/seats")
    public ResponseEntity<ApiResponse<Theater>> getTheaterWithSeats(@PathVariable Long id) {
        logger.info("Fetching theater with seats for ID: {}", id);

        try {
            Theater theater = theaterService.getTheaterWithSeats(id);
            ApiResponse<Theater> response = ApiResponse.success("Theater with seats retrieved successfully", theater);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch theater with seats for ID: {}", id, e);
            ApiResponse<Theater> response = ApiResponse.error("Failed to fetch theater with seats: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/screen-type/{screenType}")
    public ResponseEntity<ApiResponse<List<Theater>>> getTheatersByScreenType(@PathVariable String screenType) {
        logger.info("Fetching theaters by screen type: {}", screenType);

        try {
            Theater.ScreenType type = Theater.ScreenType.valueOf("SCREEN_" + screenType.toUpperCase());
            List<Theater> theaters = theaterService.getTheatersByScreenType(type);
            ApiResponse<List<Theater>> response = ApiResponse.success("Theaters by screen type retrieved successfully", theaters);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch theaters by screen type: {}", screenType, e);
            ApiResponse<List<Theater>> response = ApiResponse.error("Failed to fetch theaters by screen type: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/location")
    public ResponseEntity<ApiResponse<List<Theater>>> getTheatersByLocation(@RequestParam String location) {
        logger.info("Fetching theaters by location: {}", location);

        try {
            List<Theater> theaters = theaterService.getTheatersByLocation(location);
            ApiResponse<List<Theater>> response = ApiResponse.success("Theaters by location retrieved successfully", theaters);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch theaters by location: {}", location, e);
            ApiResponse<List<Theater>> response = ApiResponse.error("Failed to fetch theaters by location: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/search")
    public ResponseEntity<ApiResponse<List<Theater>>> searchTheatersByName(@RequestParam String name) {
        logger.info("Searching theaters by name: {}", name);

        try {
            List<Theater> theaters = theaterService.searchTheatersByName(name);
            ApiResponse<List<Theater>> response = ApiResponse.success("Theater search results retrieved successfully", theaters);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to search theaters by name: {}", name, e);
            ApiResponse<List<Theater>> response = ApiResponse.error("Failed to search theaters: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/capacity")
    public ResponseEntity<ApiResponse<List<Theater>>> getTheatersByCapacity(
            @RequestParam(required = false) Integer minSeats,
            @RequestParam(required = false) Integer maxSeats) {
        
        logger.info("Fetching theaters by capacity - min: {}, max: {}", minSeats, maxSeats);

        try {
            List<Theater> theaters;
            if (minSeats != null && maxSeats != null) {
                theaters = theaterService.getTheatersBySeatCapacityRange(minSeats, maxSeats);
            } else if (minSeats != null) {
                theaters = theaterService.getTheatersByMinimumSeats(minSeats);
            } else {
                theaters = theaterService.getTheatersByCapacityDesc();
            }
            
            ApiResponse<List<Theater>> response = ApiResponse.success("Theaters by capacity retrieved successfully", theaters);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch theaters by capacity", e);
            ApiResponse<List<Theater>> response = ApiResponse.error("Failed to fetch theaters by capacity: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/with-showtimes")
    public ResponseEntity<ApiResponse<List<Theater>>> getTheatersWithShowtimes(
            @RequestParam(required = false) String date) {
        
        logger.info("Fetching theaters with showtimes for date: {}", date);

        try {
            LocalDate showDate = date != null ? LocalDate.parse(date) : LocalDate.now();
            List<Theater> theaters = theaterService.getTheatersWithShowtimesOnDate(showDate);
            
            ApiResponse<List<Theater>> response = ApiResponse.success("Theaters with showtimes retrieved successfully", theaters);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch theaters with showtimes", e);
            ApiResponse<List<Theater>> response = ApiResponse.error("Failed to fetch theaters with showtimes: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/movie/{movieId}")
    public ResponseEntity<ApiResponse<List<Theater>>> getTheatersShowingMovie(@PathVariable Long movieId) {
        logger.info("Fetching theaters showing movie ID: {}", movieId);

        try {
            List<Theater> theaters = theaterService.getTheatersShowingMovie(movieId);
            ApiResponse<List<Theater>> response = ApiResponse.success("Theaters showing movie retrieved successfully", theaters);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch theaters showing movie ID: {}", movieId, e);
            ApiResponse<List<Theater>> response = ApiResponse.error("Failed to fetch theaters showing movie: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/3d")
    public ResponseEntity<ApiResponse<List<Theater>>> get3DTheaters() {
        logger.info("Fetching 3D theaters");

        try {
            List<Theater> theaters = theaterService.get3DTheaters();
            ApiResponse<List<Theater>> response = ApiResponse.success("3D theaters retrieved successfully", theaters);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch 3D theaters", e);
            ApiResponse<List<Theater>> response = ApiResponse.error("Failed to fetch 3D theaters: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/imax")
    public ResponseEntity<ApiResponse<List<Theater>>> getIMAXTheaters() {
        logger.info("Fetching IMAX theaters");

        try {
            List<Theater> theaters = theaterService.getIMAXTheaters();
            ApiResponse<List<Theater>> response = ApiResponse.success("IMAX theaters retrieved successfully", theaters);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to fetch IMAX theaters", e);
            ApiResponse<List<Theater>> response = ApiResponse.error("Failed to fetch IMAX theaters: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
